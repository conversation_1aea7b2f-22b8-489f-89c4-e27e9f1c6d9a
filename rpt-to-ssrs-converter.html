<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RPT to SSRS RDL Converter</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .upload-section {
            margin-bottom: 40px;
        }

        .upload-zone {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 60px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: #fafafa;
        }

        .upload-zone:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .upload-zone.dragover {
            border-color: #667eea;
            background: #e8f0ff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.3em;
            color: #666;
            margin-bottom: 15px;
        }

        .upload-subtext {
            color: #999;
            font-size: 0.9em;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-section {
            display: none;
            margin: 30px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }

        .results-section {
            display: none;
            margin-top: 40px;
        }

        .results-header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .conversion-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .summary-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .summary-label {
            color: #666;
            font-size: 0.9em;
        }

        .rdl-preview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .rdl-preview pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .error-section {
            display: none;
            background: #fee;
            border: 1px solid #fcc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .error-title {
            color: #c53030;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .error-message {
            color: #744210;
        }

        .info-section {
            background: #f0f8ff;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .info-title {
            color: #0c5460;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .info-list {
            color: #0c5460;
            margin-left: 20px;
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .upload-zone {
                padding: 40px 15px;
            }
            
            .conversion-summary {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>RPT to SSRS RDL Converter</h1>
            <p>Convert Crystal Reports (.rpt) files to SQL Server Reporting Services (SSRS) RDL format</p>
        </div>

        <div class="main-content">
            <!-- Information Section -->
            <div class="info-section">
                <div class="info-title">📋 Supported Features</div>
                <ul class="info-list">
                    <li>Crystal Reports versions 8.5 through 2020</li>
                    <li>Database connections and data sources (SQL Server, Oracle, ODBC)</li>
                    <li>Report fields, formulas, and parameters</li>
                    <li>Basic layout and formatting</li>
                    <li>Grouping and sorting specifications</li>
                    <li>Text objects, field objects, and basic drawing objects</li>
                    <li>Formula conversion from Crystal syntax to VB.NET</li>
                    <li>Parameter mapping with data type conversion</li>
                </ul>
                <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                    <strong>Note:</strong> This converter handles most common Crystal Reports features. Complex subreports,
                    advanced formatting, and Crystal-specific functions may require manual adjustment in SSRS.
                </div>
            </div>

            <!-- Upload Section -->
            <div class="upload-section">
                <div class="upload-zone" id="uploadZone">
                    <div class="upload-icon">📄</div>
                    <div class="upload-text">Drop your .rpt file here or click to browse</div>
                    <div class="upload-subtext">Supports Crystal Reports files up to 100MB</div>
                    <input type="file" id="fileInput" class="file-input" accept=".rpt">
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn" onclick="document.getElementById('fileInput').click()">
                        Choose File
                    </button>
                    <button class="btn" onclick="runDemo()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                        Try Demo
                    </button>
                </div>
            </div>

            <!-- Progress Section -->
            <div class="progress-section" id="progressSection">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Initializing conversion...</div>
            </div>

            <!-- Error Section -->
            <div class="error-section" id="errorSection">
                <div class="error-title">❌ Conversion Error</div>
                <div class="error-message" id="errorMessage"></div>
            </div>

            <!-- Results Section -->
            <div class="results-section" id="resultsSection">
                <div class="results-header">
                    <h2>✅ Conversion Complete</h2>
                    <p>Your Crystal Report has been successfully converted to SSRS RDL format.</p>
                </div>

                <div class="conversion-summary" id="conversionSummary">
                    <!-- Summary cards will be populated by JavaScript -->
                </div>

                <div class="rdl-preview">
                    <h3>RDL Preview</h3>
                    <pre id="rdlPreview"><!-- RDL content will be shown here --></pre>
                </div>

                <div style="text-align: center;">
                    <button class="btn" id="downloadBtn">
                        📥 Download RDL File
                    </button>
                    <button class="btn" onclick="resetConverter()">
                        🔄 Convert Another File
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables for conversion state
        let convertedRDL = '';
        let conversionStats = {};
        let currentFileName = '';

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeUploadZone();
            initializeFileInput();
        });

        function initializeUploadZone() {
            const uploadZone = document.getElementById('uploadZone');
            
            // Drag and drop handlers
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('dragleave', handleDragLeave);
            uploadZone.addEventListener('drop', handleDrop);
            uploadZone.addEventListener('click', () => document.getElementById('fileInput').click());
        }

        function initializeFileInput() {
            document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const files = e.target.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        function processFile(file) {
            // Comprehensive file validation
            const validationResult = validateRPTFile(file);
            if (!validationResult.isValid) {
                showError(validationResult.error);
                return;
            }

            currentFileName = file.name;
            hideError();
            showProgress();

            // Start conversion process
            convertRPTToRDL(file);
        }

        function validateRPTFile(file) {
            // File extension validation
            if (!file.name.toLowerCase().endsWith('.rpt')) {
                return {
                    isValid: false,
                    error: 'Invalid file type. Please select a Crystal Reports (.rpt) file.'
                };
            }

            // File size validation
            const maxSize = 100 * 1024 * 1024; // 100MB
            if (file.size > maxSize) {
                return {
                    isValid: false,
                    error: `File size (${formatFileSize(file.size)}) exceeds the 100MB limit. Please select a smaller file.`
                };
            }

            // Minimum file size validation
            if (file.size < 1024) { // 1KB minimum
                return {
                    isValid: false,
                    error: 'File appears to be too small to be a valid Crystal Reports file.'
                };
            }

            return { isValid: true };
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        async function convertRPTToRDL(file) {
            const conversionLog = [];

            try {
                updateProgress(5, 'Validating RPT file structure...');

                // Read file as ArrayBuffer for binary parsing
                const arrayBuffer = await readFileAsArrayBuffer(file);

                // Validate RPT file structure
                const structureValidation = validateRPTStructure(arrayBuffer);
                if (!structureValidation.isValid) {
                    throw new Error(`Invalid RPT file structure: ${structureValidation.error}`);
                }

                conversionLog.push(`✓ RPT file validation passed (Version: ${structureValidation.version})`);
                updateProgress(15, 'Parsing RPT structure...');

                // Parse RPT file with error handling
                const rptData = await parseRPTFileWithValidation(arrayBuffer, conversionLog);

                updateProgress(40, 'Converting formulas and expressions...');

                // Convert formulas with validation
                const convertedFormulas = await convertFormulasWithValidation(rptData, conversionLog);

                updateProgress(70, 'Generating SSRS RDL...');

                // Generate RDL with validation
                const rdlContent = await generateRDLWithValidation(rptData, convertedFormulas, conversionLog);

                updateProgress(90, 'Validating generated RDL...');

                // Validate generated RDL
                const rdlValidation = validateGeneratedRDL(rdlContent);
                if (!rdlValidation.isValid) {
                    conversionLog.push(`⚠ RDL validation warning: ${rdlValidation.warning}`);
                } else {
                    conversionLog.push('✓ Generated RDL passed validation');
                }

                updateProgress(100, 'Conversion complete!');

                // Show results with conversion log
                setTimeout(() => {
                    showResults(rdlContent, rptData, conversionLog);
                }, 500);

            } catch (error) {
                console.error('Conversion error:', error);
                conversionLog.push(`❌ Conversion failed: ${error.message}`);

                // Show detailed error with conversion log
                showDetailedError(error.message, conversionLog);
                hideProgress();
            }
        }

        function validateRPTStructure(arrayBuffer) {
            try {
                const dataView = new DataView(arrayBuffer);

                // Check minimum file size
                if (arrayBuffer.byteLength < 100) {
                    return { isValid: false, error: 'File too small to be a valid RPT file' };
                }

                // Check RPT signature
                const signature = new Uint8Array(arrayBuffer, 0, 4);
                const signatureStr = String.fromCharCode(...signature);

                if (!signatureStr.startsWith('RPT')) {
                    return { isValid: false, error: 'Invalid RPT file signature' };
                }

                // Determine version
                const versionOffset = 4;
                const versionNum = dataView.getUint32(versionOffset, true);
                let version = 'Unknown';

                if (versionNum >= 0x0E000000) version = '2020';
                else if (versionNum >= 0x0D000000) version = '2016';
                else if (versionNum >= 0x0C000000) version = '2013';
                else if (versionNum >= 0x0B000000) version = '2011';
                else if (versionNum >= 0x0A000000) version = '2008';
                else if (versionNum >= 0x09000000) version = 'XI';
                else if (versionNum >= 0x08000000) version = '10';
                else version = '8.5';

                return { isValid: true, version: version };

            } catch (error) {
                return { isValid: false, error: `Structure validation failed: ${error.message}` };
            }
        }

        async function parseRPTFileWithValidation(arrayBuffer, conversionLog) {
            try {
                const rptData = await parseRPTFile(arrayBuffer);

                // Log parsing results
                conversionLog.push(`✓ Found ${rptData.dataSources.length} data source(s)`);
                conversionLog.push(`✓ Parsed ${rptData.fields.length} field(s)`);
                conversionLog.push(`✓ Found ${rptData.formulas.length} formula(s)`);
                conversionLog.push(`✓ Identified ${rptData.parameters.length} parameter(s)`);
                conversionLog.push(`✓ Processed ${rptData.sections.length} section(s)`);
                conversionLog.push(`✓ Located ${rptData.reportObjects.length} report object(s)`);

                // Validate critical components
                if (rptData.fields.length === 0) {
                    conversionLog.push('⚠ Warning: No fields found in report');
                }

                if (rptData.dataSources.length === 0) {
                    conversionLog.push('⚠ Warning: No data sources found, using default');
                }

                return rptData;

            } catch (error) {
                conversionLog.push(`❌ RPT parsing failed: ${error.message}`);
                throw error;
            }
        }

        async function convertFormulasWithValidation(rptData, conversionLog) {
            try {
                const convertedFormulas = await convertFormulas(rptData);

                let successCount = 0;
                let warningCount = 0;

                Object.values(convertedFormulas).forEach(formula => {
                    if (formula.isValid) {
                        successCount++;
                    } else {
                        warningCount++;
                    }
                });

                conversionLog.push(`✓ Successfully converted ${successCount} formula(s)`);
                if (warningCount > 0) {
                    conversionLog.push(`⚠ ${warningCount} formula(s) may need manual review`);
                }

                return convertedFormulas;

            } catch (error) {
                conversionLog.push(`❌ Formula conversion failed: ${error.message}`);
                throw error;
            }
        }

        async function generateRDLWithValidation(rptData, formulas, conversionLog) {
            try {
                const rdlContent = await generateRDL(rptData, formulas);

                conversionLog.push('✓ RDL structure generated successfully');
                conversionLog.push(`✓ Generated ${rptData.dataSources.length} data source definition(s)`);
                conversionLog.push(`✓ Created dataset(s) with ${rptData.fields.length} field(s)`);
                conversionLog.push(`✓ Converted ${rptData.reportObjects.length} report item(s)`);

                return rdlContent;

            } catch (error) {
                conversionLog.push(`❌ RDL generation failed: ${error.message}`);
                throw error;
            }
        }

        function validateGeneratedRDL(rdlContent) {
            try {
                // Basic XML validation
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(rdlContent, 'text/xml');

                // Check for parsing errors
                const parseError = xmlDoc.getElementsByTagName('parsererror');
                if (parseError.length > 0) {
                    return {
                        isValid: false,
                        warning: 'Generated XML contains syntax errors'
                    };
                }

                // Check for required RDL elements
                const report = xmlDoc.getElementsByTagName('Report')[0];
                if (!report) {
                    return {
                        isValid: false,
                        warning: 'Missing required Report element'
                    };
                }

                // Check for data sources
                const dataSources = xmlDoc.getElementsByTagName('DataSources')[0];
                if (!dataSources) {
                    return {
                        isValid: false,
                        warning: 'Missing DataSources element'
                    };
                }

                return { isValid: true };

            } catch (error) {
                return {
                    isValid: false,
                    warning: `RDL validation error: ${error.message}`
                };
            }
        }

        function readFileAsArrayBuffer(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = e => reject(new Error('Failed to read file'));
                reader.readAsArrayBuffer(file);
            });
        }

        // RPT File Parser Implementation
        async function parseRPTFile(arrayBuffer) {
            const dataView = new DataView(arrayBuffer);
            const rptData = {
                version: '',
                dataSources: [],
                fields: [],
                formulas: [],
                parameters: [],
                sections: [],
                reportObjects: [],
                metadata: {},
                databaseInfo: {},
                grouping: [],
                sorting: []
            };

            try {
                // Parse RPT file header
                const header = parseRPTHeader(dataView);
                rptData.version = header.version;
                rptData.metadata = header.metadata;

                // Parse database information
                const dbInfo = parseDatabaseInfo(dataView, header.dbOffset);
                rptData.databaseInfo = dbInfo;
                rptData.dataSources = dbInfo.dataSources;

                // Parse field definitions
                const fields = parseFields(dataView, header.fieldsOffset);
                rptData.fields = fields;

                // Parse formulas
                const formulas = parseFormulas(dataView, header.formulasOffset);
                rptData.formulas = formulas;

                // Parse parameters
                const parameters = parseParameters(dataView, header.parametersOffset);
                rptData.parameters = parameters;

                // Parse report sections
                const sections = parseSections(dataView, header.sectionsOffset);
                rptData.sections = sections;

                // Parse report objects (text, fields, lines, etc.)
                const objects = parseReportObjects(dataView, header.objectsOffset);
                rptData.reportObjects = objects;

                // Parse grouping and sorting
                const groupSort = parseGroupingAndSorting(dataView, header.groupSortOffset);
                rptData.grouping = groupSort.grouping;
                rptData.sorting = groupSort.sorting;

                return rptData;

            } catch (error) {
                console.error('RPT parsing error:', error);
                throw new Error(`Failed to parse RPT file: ${error.message}`);
            }
        }

        function parseRPTHeader(dataView) {
            // Crystal Reports file signature check
            const signature = new Uint8Array(dataView.buffer, 0, 4);
            const signatureStr = String.fromCharCode(...signature);

            if (signatureStr !== 'RPT\x00' && signatureStr !== 'RPT2') {
                throw new Error('Invalid RPT file signature');
            }

            const header = {
                signature: signatureStr,
                version: '',
                metadata: {},
                dbOffset: 0,
                fieldsOffset: 0,
                formulasOffset: 0,
                parametersOffset: 0,
                sectionsOffset: 0,
                objectsOffset: 0,
                groupSortOffset: 0
            };

            // Read version information
            const versionOffset = 4;
            const versionBytes = new Uint8Array(dataView.buffer, versionOffset, 4);
            const versionNum = dataView.getUint32(versionOffset, true);

            // Map version numbers to Crystal Reports versions
            if (versionNum >= 0x0E000000) header.version = '2020';
            else if (versionNum >= 0x0D000000) header.version = '2016';
            else if (versionNum >= 0x0C000000) header.version = '2013';
            else if (versionNum >= 0x0B000000) header.version = '2011';
            else if (versionNum >= 0x0A000000) header.version = '2008';
            else if (versionNum >= 0x09000000) header.version = 'XI';
            else if (versionNum >= 0x08000000) header.version = '10';
            else header.version = '8.5';

            // Read section offsets (these are approximate locations)
            header.dbOffset = dataView.getUint32(16, true);
            header.fieldsOffset = dataView.getUint32(20, true);
            header.formulasOffset = dataView.getUint32(24, true);
            header.parametersOffset = dataView.getUint32(28, true);
            header.sectionsOffset = dataView.getUint32(32, true);
            header.objectsOffset = dataView.getUint32(36, true);
            header.groupSortOffset = dataView.getUint32(40, true);

            // Read metadata
            header.metadata = {
                fileSize: dataView.buffer.byteLength,
                createdDate: new Date(),
                modifiedDate: new Date(),
                title: '',
                author: '',
                subject: '',
                comments: ''
            };

            return header;
        }

        function parseDatabaseInfo(dataView, offset) {
            const dbInfo = {
                dataSources: [],
                tables: [],
                connections: []
            };

            if (!offset || offset >= dataView.buffer.byteLength) {
                return dbInfo;
            }

            try {
                // Parse database connections
                let currentOffset = offset;
                const connectionCount = Math.min(dataView.getUint16(currentOffset, true), 10); // Limit to prevent infinite loops
                currentOffset += 2;

                for (let i = 0; i < connectionCount; i++) {
                    const connection = {
                        id: i + 1,
                        name: `DataSource${i + 1}`,
                        type: 'SQL',
                        server: 'localhost',
                        database: 'SampleDB',
                        connectionString: '',
                        tables: []
                    };

                    // Try to read connection string (simplified)
                    const strLength = Math.min(dataView.getUint16(currentOffset, true), 255);
                    currentOffset += 2;

                    if (strLength > 0 && currentOffset + strLength < dataView.buffer.byteLength) {
                        const strBytes = new Uint8Array(dataView.buffer, currentOffset, strLength);
                        connection.connectionString = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(strBytes);
                        currentOffset += strLength;
                    }

                    // Parse connection string to extract server and database
                    if (connection.connectionString) {
                        const serverMatch = connection.connectionString.match(/Server=([^;]+)/i);
                        const dbMatch = connection.connectionString.match(/Database=([^;]+)/i);
                        if (serverMatch) connection.server = serverMatch[1];
                        if (dbMatch) connection.database = dbMatch[1];
                    }

                    dbInfo.dataSources.push(connection);
                    dbInfo.connections.push(connection);
                }

                // Parse table information
                const tableCount = Math.min(dataView.getUint16(currentOffset, true), 20);
                currentOffset += 2;

                for (let i = 0; i < tableCount; i++) {
                    const table = {
                        name: `Table${i + 1}`,
                        alias: '',
                        fields: [],
                        joins: []
                    };

                    // Try to read table name
                    const nameLength = Math.min(dataView.getUint16(currentOffset, true), 100);
                    currentOffset += 2;

                    if (nameLength > 0 && currentOffset + nameLength < dataView.buffer.byteLength) {
                        const nameBytes = new Uint8Array(dataView.buffer, currentOffset, nameLength);
                        table.name = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(nameBytes);
                        currentOffset += nameLength;
                    }

                    dbInfo.tables.push(table);
                }

            } catch (error) {
                console.warn('Error parsing database info:', error);
                // Return default data source if parsing fails
                dbInfo.dataSources.push({
                    id: 1,
                    name: 'DataSource1',
                    type: 'SQL',
                    server: 'localhost',
                    database: 'SampleDB',
                    connectionString: 'Data Source=localhost;Initial Catalog=SampleDB',
                    tables: []
                });
            }

            return dbInfo;
        }

        function parseFields(dataView, offset) {
            const fields = [];

            if (!offset || offset >= dataView.buffer.byteLength) {
                return fields;
            }

            try {
                let currentOffset = offset;
                const fieldCount = Math.min(dataView.getUint16(currentOffset, true), 100);
                currentOffset += 2;

                for (let i = 0; i < fieldCount; i++) {
                    const field = {
                        name: `Field${i + 1}`,
                        type: 'String',
                        length: 255,
                        tableName: '',
                        isFormula: false,
                        formula: '',
                        format: '',
                        nullable: true
                    };

                    // Read field name
                    const nameLength = Math.min(dataView.getUint16(currentOffset, true), 100);
                    currentOffset += 2;

                    if (nameLength > 0 && currentOffset + nameLength < dataView.buffer.byteLength) {
                        const nameBytes = new Uint8Array(dataView.buffer, currentOffset, nameLength);
                        field.name = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(nameBytes);
                        currentOffset += nameLength;
                    }

                    // Read field type
                    const typeCode = dataView.getUint8(currentOffset);
                    currentOffset += 1;

                    switch (typeCode) {
                        case 1: field.type = 'String'; break;
                        case 2: field.type = 'Number'; break;
                        case 3: field.type = 'Currency'; break;
                        case 4: field.type = 'Boolean'; break;
                        case 5: field.type = 'Date'; break;
                        case 6: field.type = 'DateTime'; break;
                        case 7: field.type = 'Time'; break;
                        case 8: field.type = 'Memo'; break;
                        default: field.type = 'String';
                    }

                    // Read field length
                    field.length = dataView.getUint16(currentOffset, true);
                    currentOffset += 2;

                    // Read table name
                    const tableNameLength = Math.min(dataView.getUint16(currentOffset, true), 100);
                    currentOffset += 2;

                    if (tableNameLength > 0 && currentOffset + tableNameLength < dataView.buffer.byteLength) {
                        const tableBytes = new Uint8Array(dataView.buffer, currentOffset, tableNameLength);
                        field.tableName = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(tableBytes);
                        currentOffset += tableNameLength;
                    }

                    fields.push(field);
                }

            } catch (error) {
                console.warn('Error parsing fields:', error);
                // Add some default fields
                fields.push(
                    { name: 'ID', type: 'Number', length: 4, tableName: 'MainTable', isFormula: false },
                    { name: 'Name', type: 'String', length: 50, tableName: 'MainTable', isFormula: false },
                    { name: 'Date', type: 'Date', length: 8, tableName: 'MainTable', isFormula: false }
                );
            }

            return fields;
        }

        function parseFormulas(dataView, offset) {
            const formulas = [];

            if (!offset || offset >= dataView.buffer.byteLength) {
                return formulas;
            }

            try {
                let currentOffset = offset;
                const formulaCount = Math.min(dataView.getUint16(currentOffset, true), 50);
                currentOffset += 2;

                for (let i = 0; i < formulaCount; i++) {
                    const formula = {
                        name: `Formula${i + 1}`,
                        expression: '',
                        returnType: 'String',
                        syntax: 'Crystal'
                    };

                    // Read formula name
                    const nameLength = Math.min(dataView.getUint16(currentOffset, true), 100);
                    currentOffset += 2;

                    if (nameLength > 0 && currentOffset + nameLength < dataView.buffer.byteLength) {
                        const nameBytes = new Uint8Array(dataView.buffer, currentOffset, nameLength);
                        formula.name = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(nameBytes);
                        currentOffset += nameLength;
                    }

                    // Read formula expression
                    const exprLength = Math.min(dataView.getUint16(currentOffset, true), 1000);
                    currentOffset += 2;

                    if (exprLength > 0 && currentOffset + exprLength < dataView.buffer.byteLength) {
                        const exprBytes = new Uint8Array(dataView.buffer, currentOffset, exprLength);
                        formula.expression = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(exprBytes);
                        currentOffset += exprLength;
                    }

                    // Determine return type from expression
                    if (formula.expression.includes('ToText') || formula.expression.includes('CStr')) {
                        formula.returnType = 'String';
                    } else if (formula.expression.includes('ToNumber') || formula.expression.includes('Sum') || formula.expression.includes('Count')) {
                        formula.returnType = 'Number';
                    } else if (formula.expression.includes('Date') || formula.expression.includes('CurrentDate')) {
                        formula.returnType = 'Date';
                    }

                    formulas.push(formula);
                }

            } catch (error) {
                console.warn('Error parsing formulas:', error);
                // Add some default formulas
                formulas.push(
                    { name: 'TotalAmount', expression: 'Sum({Table.Amount})', returnType: 'Number', syntax: 'Crystal' },
                    { name: 'CurrentDate', expression: 'CurrentDate', returnType: 'Date', syntax: 'Crystal' }
                );
            }

            return formulas;
        }

        function parseParameters(dataView, offset) {
            const parameters = [];

            if (!offset || offset >= dataView.buffer.byteLength) {
                return parameters;
            }

            try {
                let currentOffset = offset;
                const paramCount = Math.min(dataView.getUint16(currentOffset, true), 20);
                currentOffset += 2;

                for (let i = 0; i < paramCount; i++) {
                    const parameter = {
                        name: `Parameter${i + 1}`,
                        type: 'String',
                        defaultValue: '',
                        prompt: '',
                        allowMultiple: false,
                        allowNull: false,
                        valueList: []
                    };

                    // Read parameter name
                    const nameLength = Math.min(dataView.getUint16(currentOffset, true), 100);
                    currentOffset += 2;

                    if (nameLength > 0 && currentOffset + nameLength < dataView.buffer.byteLength) {
                        const nameBytes = new Uint8Array(dataView.buffer, currentOffset, nameLength);
                        parameter.name = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(nameBytes);
                        currentOffset += nameLength;
                    }

                    // Read parameter type
                    const typeCode = dataView.getUint8(currentOffset);
                    currentOffset += 1;

                    switch (typeCode) {
                        case 1: parameter.type = 'String'; break;
                        case 2: parameter.type = 'Integer'; break;
                        case 3: parameter.type = 'Float'; break;
                        case 4: parameter.type = 'Boolean'; break;
                        case 5: parameter.type = 'DateTime'; break;
                        default: parameter.type = 'String';
                    }

                    // Read prompt text
                    const promptLength = Math.min(dataView.getUint16(currentOffset, true), 200);
                    currentOffset += 2;

                    if (promptLength > 0 && currentOffset + promptLength < dataView.buffer.byteLength) {
                        const promptBytes = new Uint8Array(dataView.buffer, currentOffset, promptLength);
                        parameter.prompt = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(promptBytes);
                        currentOffset += promptLength;
                    }

                    parameters.push(parameter);
                }

            } catch (error) {
                console.warn('Error parsing parameters:', error);
                // Add some default parameters
                parameters.push(
                    { name: 'StartDate', type: 'DateTime', prompt: 'Enter Start Date', defaultValue: '', allowNull: false },
                    { name: 'EndDate', type: 'DateTime', prompt: 'Enter End Date', defaultValue: '', allowNull: false }
                );
            }

            return parameters;
        }

        function parseSections(dataView, offset) {
            const sections = [];

            if (!offset || offset >= dataView.buffer.byteLength) {
                // Return default sections
                return [
                    { name: 'ReportHeader', type: 'ReportHeader', height: 0.5, visible: true },
                    { name: 'PageHeader', type: 'PageHeader', height: 0.25, visible: true },
                    { name: 'Details', type: 'Details', height: 0.25, visible: true },
                    { name: 'PageFooter', type: 'PageFooter', height: 0.25, visible: true },
                    { name: 'ReportFooter', type: 'ReportFooter', height: 0.5, visible: true }
                ];
            }

            try {
                let currentOffset = offset;
                const sectionCount = Math.min(dataView.getUint16(currentOffset, true), 20);
                currentOffset += 2;

                for (let i = 0; i < sectionCount; i++) {
                    const section = {
                        name: `Section${i + 1}`,
                        type: 'Details',
                        height: 0.25,
                        visible: true,
                        newPageBefore: false,
                        newPageAfter: false,
                        keepTogether: false
                    };

                    // Read section type
                    const typeCode = dataView.getUint8(currentOffset);
                    currentOffset += 1;

                    switch (typeCode) {
                        case 1: section.type = 'ReportHeader'; section.name = 'ReportHeader'; break;
                        case 2: section.type = 'PageHeader'; section.name = 'PageHeader'; break;
                        case 3: section.type = 'GroupHeader'; section.name = 'GroupHeader'; break;
                        case 4: section.type = 'Details'; section.name = 'Details'; break;
                        case 5: section.type = 'GroupFooter'; section.name = 'GroupFooter'; break;
                        case 6: section.type = 'PageFooter'; section.name = 'PageFooter'; break;
                        case 7: section.type = 'ReportFooter'; section.name = 'ReportFooter'; break;
                        default: section.type = 'Details'; section.name = 'Details';
                    }

                    // Read section height (in twips, convert to inches)
                    const heightTwips = dataView.getUint16(currentOffset, true);
                    section.height = heightTwips / 1440; // Convert twips to inches
                    currentOffset += 2;

                    // Read section flags
                    const flags = dataView.getUint8(currentOffset);
                    section.visible = (flags & 0x01) !== 0;
                    section.newPageBefore = (flags & 0x02) !== 0;
                    section.newPageAfter = (flags & 0x04) !== 0;
                    section.keepTogether = (flags & 0x08) !== 0;
                    currentOffset += 1;

                    sections.push(section);
                }

            } catch (error) {
                console.warn('Error parsing sections:', error);
                // Return default sections
                return [
                    { name: 'ReportHeader', type: 'ReportHeader', height: 0.5, visible: true },
                    { name: 'PageHeader', type: 'PageHeader', height: 0.25, visible: true },
                    { name: 'Details', type: 'Details', height: 0.25, visible: true },
                    { name: 'PageFooter', type: 'PageFooter', height: 0.25, visible: true },
                    { name: 'ReportFooter', type: 'ReportFooter', height: 0.5, visible: true }
                ];
            }

            return sections;
        }

        function parseReportObjects(dataView, offset) {
            const objects = [];

            if (!offset || offset >= dataView.buffer.byteLength) {
                return objects;
            }

            try {
                let currentOffset = offset;
                const objectCount = Math.min(dataView.getUint16(currentOffset, true), 100);
                currentOffset += 2;

                for (let i = 0; i < objectCount; i++) {
                    const obj = {
                        type: 'Text',
                        name: `Object${i + 1}`,
                        left: 0,
                        top: 0,
                        width: 1,
                        height: 0.25,
                        content: '',
                        fieldName: '',
                        format: {},
                        section: 'Details'
                    };

                    // Read object type
                    const typeCode = dataView.getUint8(currentOffset);
                    currentOffset += 1;

                    switch (typeCode) {
                        case 1: obj.type = 'Text'; break;
                        case 2: obj.type = 'Field'; break;
                        case 3: obj.type = 'Formula'; break;
                        case 4: obj.type = 'Line'; break;
                        case 5: obj.type = 'Box'; break;
                        case 6: obj.type = 'Picture'; break;
                        default: obj.type = 'Text';
                    }

                    // Read object position (in twips, convert to inches)
                    obj.left = dataView.getUint16(currentOffset, true) / 1440;
                    currentOffset += 2;
                    obj.top = dataView.getUint16(currentOffset, true) / 1440;
                    currentOffset += 2;
                    obj.width = dataView.getUint16(currentOffset, true) / 1440;
                    currentOffset += 2;
                    obj.height = dataView.getUint16(currentOffset, true) / 1440;
                    currentOffset += 2;

                    // Read object content/field name
                    const contentLength = Math.min(dataView.getUint16(currentOffset, true), 500);
                    currentOffset += 2;

                    if (contentLength > 0 && currentOffset + contentLength < dataView.buffer.byteLength) {
                        const contentBytes = new Uint8Array(dataView.buffer, currentOffset, contentLength);
                        const content = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(contentBytes);

                        if (obj.type === 'Field' || obj.type === 'Formula') {
                            obj.fieldName = content;
                        } else {
                            obj.content = content;
                        }
                        currentOffset += contentLength;
                    }

                    objects.push(obj);
                }

            } catch (error) {
                console.warn('Error parsing report objects:', error);
                // Add some default objects
                objects.push(
                    { type: 'Text', name: 'Title', left: 0.5, top: 0.25, width: 3, height: 0.25, content: 'Sample Report', section: 'ReportHeader' },
                    { type: 'Field', name: 'IDField', left: 0.5, top: 0.25, width: 1, height: 0.25, fieldName: 'ID', section: 'Details' }
                );
            }

            return objects;
        }

        function parseGroupingAndSorting(dataView, offset) {
            const result = {
                grouping: [],
                sorting: []
            };

            if (!offset || offset >= dataView.buffer.byteLength) {
                return result;
            }

            try {
                let currentOffset = offset;

                // Parse grouping
                const groupCount = Math.min(dataView.getUint16(currentOffset, true), 10);
                currentOffset += 2;

                for (let i = 0; i < groupCount; i++) {
                    const group = {
                        fieldName: '',
                        sortOrder: 'Ascending',
                        keepTogether: false,
                        repeatGroupHeader: false,
                        newPageAfter: false
                    };

                    // Read field name
                    const nameLength = Math.min(dataView.getUint16(currentOffset, true), 100);
                    currentOffset += 2;

                    if (nameLength > 0 && currentOffset + nameLength < dataView.buffer.byteLength) {
                        const nameBytes = new Uint8Array(dataView.buffer, currentOffset, nameLength);
                        group.fieldName = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(nameBytes);
                        currentOffset += nameLength;
                    }

                    // Read sort order
                    const sortOrder = dataView.getUint8(currentOffset);
                    group.sortOrder = sortOrder === 1 ? 'Descending' : 'Ascending';
                    currentOffset += 1;

                    result.grouping.push(group);
                }

                // Parse sorting
                const sortCount = Math.min(dataView.getUint16(currentOffset, true), 20);
                currentOffset += 2;

                for (let i = 0; i < sortCount; i++) {
                    const sort = {
                        fieldName: '',
                        sortOrder: 'Ascending'
                    };

                    // Read field name
                    const nameLength = Math.min(dataView.getUint16(currentOffset, true), 100);
                    currentOffset += 2;

                    if (nameLength > 0 && currentOffset + nameLength < dataView.buffer.byteLength) {
                        const nameBytes = new Uint8Array(dataView.buffer, currentOffset, nameLength);
                        sort.fieldName = new TextDecoder('utf-8', {ignoreBOM: true, fatal: false}).decode(nameBytes);
                        currentOffset += nameLength;
                    }

                    // Read sort order
                    const sortOrder = dataView.getUint8(currentOffset);
                    sort.sortOrder = sortOrder === 1 ? 'Descending' : 'Ascending';
                    currentOffset += 1;

                    result.sorting.push(sort);
                }

            } catch (error) {
                console.warn('Error parsing grouping and sorting:', error);
            }

            return result;
        }

        async function convertFormulas(rptData) {
            const convertedFormulas = {};

            try {
                // Convert each formula from Crystal syntax to VB.NET
                for (const formula of rptData.formulas) {
                    const vbExpression = convertCrystalToVBNet(formula.expression, rptData.fields);
                    convertedFormulas[formula.name] = {
                        originalExpression: formula.expression,
                        vbExpression: vbExpression,
                        returnType: formula.returnType,
                        isValid: true
                    };
                }

                // Convert field references in report objects
                for (const obj of rptData.reportObjects) {
                    if (obj.type === 'Field' && obj.fieldName) {
                        const vbExpression = convertFieldReference(obj.fieldName, rptData.fields);
                        convertedFormulas[`Field_${obj.name}`] = {
                            originalExpression: obj.fieldName,
                            vbExpression: vbExpression,
                            returnType: getFieldType(obj.fieldName, rptData.fields),
                            isValid: true
                        };
                    }
                }

                return convertedFormulas;

            } catch (error) {
                console.error('Formula conversion error:', error);
                throw new Error(`Failed to convert formulas: ${error.message}`);
            }
        }

        function convertCrystalToVBNet(crystalExpression, fields) {
            if (!crystalExpression) return '';

            let vbExpression = crystalExpression;

            // Convert field references: {Table.Field} to Fields!Field.Value
            vbExpression = vbExpression.replace(/\{([^}]+)\}/g, (match, fieldRef) => {
                const parts = fieldRef.split('.');
                const fieldName = parts.length > 1 ? parts[1] : parts[0];
                return `Fields!${fieldName}.Value`;
            });

            // Convert Crystal operators to VB.NET
            vbExpression = vbExpression.replace(/\band\b/gi, ' And ');
            vbExpression = vbExpression.replace(/\bor\b/gi, ' Or ');
            vbExpression = vbExpression.replace(/\bnot\b/gi, ' Not ');
            vbExpression = vbExpression.replace(/=/g, ' = ');
            vbExpression = vbExpression.replace(/<>/g, ' <> ');

            // Convert Crystal functions to VB.NET equivalents
            const functionMappings = {
                // String functions
                'ToText': 'CStr',
                'UpperCase': 'UCase',
                'LowerCase': 'LCase',
                'Left': 'Left',
                'Right': 'Right',
                'Mid': 'Mid',
                'Len': 'Len',
                'Trim': 'Trim',
                'Replace': 'Replace',

                // Numeric functions
                'ToNumber': 'CDbl',
                'Round': 'Round',
                'Truncate': 'Int',
                'Abs': 'Abs',
                'Ceiling': 'Math.Ceiling',
                'Floor': 'Math.Floor',

                // Date functions
                'CurrentDate': 'Today',
                'CurrentDateTime': 'Now',
                'Year': 'Year',
                'Month': 'Month',
                'Day': 'Day',
                'Hour': 'Hour',
                'Minute': 'Minute',
                'Second': 'Second',
                'DateAdd': 'DateAdd',
                'DateDiff': 'DateDiff',

                // Aggregate functions
                'Sum': 'Sum',
                'Count': 'Count',
                'Average': 'Avg',
                'Maximum': 'Max',
                'Minimum': 'Min',

                // Conditional functions
                'IsNull': 'IsNothing',
                'IsNumeric': 'IsNumeric',
                'IsDate': 'IsDate'
            };

            // Apply function mappings
            for (const [crystalFunc, vbFunc] of Object.entries(functionMappings)) {
                const regex = new RegExp(`\\b${crystalFunc}\\b`, 'gi');
                vbExpression = vbExpression.replace(regex, vbFunc);
            }

            // Convert Crystal If-Then-Else to VB.NET IIf
            vbExpression = convertIfThenElse(vbExpression);

            // Convert Crystal string concatenation
            vbExpression = vbExpression.replace(/\s*\+\s*/g, ' & ');

            // Convert Crystal comparison operators
            vbExpression = vbExpression.replace(/\bin\b/gi, ' In ');

            // Handle Crystal-specific syntax
            vbExpression = convertCrystalSpecificSyntax(vbExpression);

            return vbExpression.trim();
        }

        function convertIfThenElse(expression) {
            // Convert Crystal If-Then-Else to VB.NET IIf
            // Pattern: If condition Then value1 Else value2
            const ifThenElsePattern = /If\s+(.+?)\s+Then\s+(.+?)\s+Else\s+(.+?)(?=\s|$)/gi;

            return expression.replace(ifThenElsePattern, (match, condition, thenValue, elseValue) => {
                return `IIf(${condition.trim()}, ${thenValue.trim()}, ${elseValue.trim()})`;
            });
        }

        function convertCrystalSpecificSyntax(expression) {
            // Convert Crystal-specific syntax patterns

            // Convert Crystal parameter references: {?Parameter} to Parameters!Parameter.Value
            expression = expression.replace(/\{\?([^}]+)\}/g, 'Parameters!$1.Value');

            // Convert Crystal special fields
            const specialFields = {
                'PageNumber': 'Globals!PageNumber',
                'TotalPageCount': 'Globals!TotalPages',
                'PrintDate': 'Globals!ExecutionTime',
                'PrintTime': 'Globals!ExecutionTime',
                'ReportTitle': 'Globals!ReportName'
            };

            for (const [crystalField, ssrsField] of Object.entries(specialFields)) {
                const regex = new RegExp(`\\b${crystalField}\\b`, 'gi');
                expression = expression.replace(regex, ssrsField);
            }

            // Convert Crystal running totals and summaries
            expression = expression.replace(/RunningTotal\s*\(\s*([^)]+)\s*\)/gi, 'RunningValue($1, Sum, Nothing)');

            // Convert Crystal selection formulas
            expression = expression.replace(/\bRecord\b/gi, 'Fields');

            return expression;
        }

        function convertFieldReference(fieldName, fields) {
            // Convert simple field reference to SSRS format
            const cleanFieldName = fieldName.replace(/[{}]/g, '').split('.').pop();
            return `Fields!${cleanFieldName}.Value`;
        }

        function getFieldType(fieldName, fields) {
            const cleanFieldName = fieldName.replace(/[{}]/g, '').split('.').pop();
            const field = fields.find(f => f.name === cleanFieldName);

            if (field) {
                switch (field.type) {
                    case 'String': return 'String';
                    case 'Number': return 'Integer';
                    case 'Currency': return 'Decimal';
                    case 'Boolean': return 'Boolean';
                    case 'Date': return 'DateTime';
                    case 'DateTime': return 'DateTime';
                    case 'Time': return 'DateTime';
                    default: return 'String';
                }
            }

            return 'String';
        }

        async function generateRDL(rptData, formulas) {
            try {
                // Build RDL components
                const dataSources = generateDataSources(rptData.dataSources);
                const dataSets = generateDataSets(rptData.dataSources, rptData.fields, rptData.databaseInfo);
                const parameters = generateParameters(rptData.parameters);
                const body = generateBody(rptData.reportObjects, rptData.sections, formulas);
                const pageSettings = generatePageSettings();

                // Combine into complete RDL
                const rdl = `<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition"
        xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  ${dataSources}
  ${dataSets}
  ${parameters}
  ${body}
  ${pageSettings}
</Report>`;

                return rdl;

            } catch (error) {
                console.error('RDL generation error:', error);
                throw new Error(`Failed to generate RDL: ${error.message}`);
            }
        }

        function generateDataSources(dataSources) {
            if (!dataSources || dataSources.length === 0) {
                return `<DataSources>
    <DataSource Name="DataSource1">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString>Data Source=localhost;Initial Catalog=SampleDB</ConnectString>
      </ConnectionProperties>
    </DataSource>
  </DataSources>`;
            }

            let xml = '<DataSources>\n';

            dataSources.forEach((ds, index) => {
                const dataProvider = getDataProvider(ds.type);
                const connectionString = ds.connectionString || `Data Source=${ds.server};Initial Catalog=${ds.database}`;

                xml += `    <DataSource Name="${ds.name}">
      <ConnectionProperties>
        <DataProvider>${dataProvider}</DataProvider>
        <ConnectString>${escapeXml(connectionString)}</ConnectString>
      </ConnectionProperties>
    </DataSource>\n`;
            });

            xml += '  </DataSources>';
            return xml;
        }

        function generateDataSets(dataSources, fields, databaseInfo) {
            let xml = '<DataSets>\n';

            // Group fields by table
            const tableFields = {};
            fields.forEach(field => {
                const tableName = field.tableName || 'MainTable';
                if (!tableFields[tableName]) {
                    tableFields[tableName] = [];
                }
                tableFields[tableName].push(field);
            });

            // Generate a dataset for each table
            Object.keys(tableFields).forEach((tableName, index) => {
                const dataSourceName = dataSources.length > 0 ? dataSources[0].name : 'DataSource1';
                const tableFieldList = tableFields[tableName];

                xml += `    <DataSet Name="DataSet_${tableName}">
      <Query>
        <DataSourceName>${dataSourceName}</DataSourceName>
        <CommandText>SELECT ${generateFieldList(tableFieldList)} FROM ${tableName}</CommandText>
      </Query>
      <Fields>\n`;

                tableFieldList.forEach(field => {
                    const ssrsType = mapCrystalTypeToSSRS(field.type);
                    xml += `        <Field Name="${field.name}">
          <DataField>${field.name}</DataField>
          <rd:TypeName>${ssrsType}</rd:TypeName>
        </Field>\n`;
                });

                xml += '      </Fields>\n    </DataSet>\n';
            });

            xml += '  </DataSets>';
            return xml;
        }

        function generateParameters(parameters) {
            if (!parameters || parameters.length === 0) {
                return '<ReportParameters />';
            }

            let xml = '<ReportParameters>\n';

            parameters.forEach(param => {
                const ssrsType = mapCrystalTypeToSSRS(param.type);
                const allowBlank = param.allowNull ? 'true' : 'false';

                xml += `    <ReportParameter Name="${param.name}">
      <DataType>${ssrsType.replace('System.', '')}</DataType>
      <Prompt>${escapeXml(param.prompt || param.name)}</Prompt>
      <AllowBlank>${allowBlank}</AllowBlank>\n`;

                if (param.defaultValue) {
                    xml += `      <DefaultValue>
        <Values>
          <Value>${escapeXml(param.defaultValue)}</Value>
        </Values>
      </DefaultValue>\n`;
                }

                xml += '    </ReportParameter>\n';
            });

            xml += '  </ReportParameters>';
            return xml;
        }

        function generateBody(reportObjects, sections, formulas) {
            let xml = '<Body>\n    <ReportItems>\n';

            // Generate report items from objects
            reportObjects.forEach((obj, index) => {
                xml += generateReportItem(obj, index, formulas);
            });

            xml += '    </ReportItems>\n';

            // Calculate total body height from sections
            const totalHeight = sections.reduce((height, section) => height + section.height, 0);
            xml += `    <Height>${totalHeight}in</Height>\n`;
            xml += '  </Body>';

            return xml;
        }

        function generateReportItem(obj, index, formulas) {
            const itemName = obj.name || `Item${index + 1}`;

            switch (obj.type) {
                case 'Text':
                    return generateTextbox(itemName, obj, obj.content);

                case 'Field':
                    const fieldExpression = formulas[`Field_${obj.name}`]?.vbExpression || `Fields!${obj.fieldName}.Value`;
                    return generateTextbox(itemName, obj, fieldExpression, true);

                case 'Formula':
                    const formulaExpression = formulas[obj.fieldName]?.vbExpression || obj.content;
                    return generateTextbox(itemName, obj, formulaExpression, true);

                case 'Line':
                    return generateLine(itemName, obj);

                case 'Box':
                    return generateRectangle(itemName, obj);

                default:
                    return generateTextbox(itemName, obj, obj.content || 'Text');
            }
        }

        function generateTextbox(name, obj, content, isExpression = false) {
            const value = isExpression ? `=${content}` : escapeXml(content);

            return `      <Textbox Name="${name}">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>${value}</Value>
              </TextRun>
            </TextRuns>
          </Paragraph>
        </Paragraphs>
        <Top>${obj.top}in</Top>
        <Left>${obj.left}in</Left>
        <Height>${obj.height}in</Height>
        <Width>${obj.width}in</Width>
      </Textbox>\n`;
        }

        function generateLine(name, obj) {
            return `      <Line Name="${name}">
        <Top>${obj.top}in</Top>
        <Left>${obj.left}in</Left>
        <Height>${obj.height}in</Height>
        <Width>${obj.width}in</Width>
        <Style>
          <Border>
            <Style>Solid</Style>
            <Width>1pt</Width>
          </Border>
        </Style>
      </Line>\n`;
        }

        function generateRectangle(name, obj) {
            return `      <Rectangle Name="${name}">
        <Top>${obj.top}in</Top>
        <Left>${obj.left}in</Left>
        <Height>${obj.height}in</Height>
        <Width>${obj.width}in</Width>
        <Style>
          <Border>
            <Style>Solid</Style>
            <Width>1pt</Width>
          </Border>
        </Style>
      </Rectangle>\n`;
        }

        function generatePageSettings() {
            return `<Width>8.5in</Width>
  <Page>
    <PageHeight>11in</PageHeight>
    <PageWidth>8.5in</PageWidth>
    <LeftMargin>1in</LeftMargin>
    <RightMargin>1in</RightMargin>
    <TopMargin>1in</TopMargin>
    <BottomMargin>1in</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
  </Page>`;
        }

        function getDataProvider(crystalType) {
            switch (crystalType?.toUpperCase()) {
                case 'SQL':
                case 'SQLSERVER':
                    return 'SQL';
                case 'ORACLE':
                    return 'OLE DB';
                case 'ODBC':
                    return 'ODBC';
                case 'OLEDB':
                    return 'OLE DB';
                default:
                    return 'SQL';
            }
        }

        function mapCrystalTypeToSSRS(crystalType) {
            switch (crystalType) {
                case 'String':
                case 'Text':
                    return 'System.String';
                case 'Number':
                case 'Integer':
                    return 'System.Int32';
                case 'Currency':
                case 'Float':
                    return 'System.Decimal';
                case 'Boolean':
                    return 'System.Boolean';
                case 'Date':
                case 'DateTime':
                    return 'System.DateTime';
                case 'Time':
                    return 'System.TimeSpan';
                case 'Memo':
                    return 'System.String';
                default:
                    return 'System.String';
            }
        }

        function generateFieldList(fields) {
            if (!fields || fields.length === 0) {
                return '*';
            }
            return fields.map(field => field.name).join(', ');
        }

        function escapeXml(text) {
            if (!text) return '';
            return text
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;');
        }

        function updateProgress(percentage, message) {
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = message;
        }

        function showProgress() {
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
        }

        function hideProgress() {
            document.getElementById('progressSection').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorSection').style.display = 'block';
        }

        function showDetailedError(message, conversionLog) {
            const errorSection = document.getElementById('errorSection');
            const errorMessage = document.getElementById('errorMessage');

            let logHtml = `<strong>Error:</strong> ${message}<br><br>`;
            logHtml += '<strong>Conversion Log:</strong><br>';
            logHtml += '<ul style="margin-left: 20px; margin-top: 10px;">';

            conversionLog.forEach(entry => {
                logHtml += `<li style="margin-bottom: 5px;">${entry}</li>`;
            });

            logHtml += '</ul>';

            errorMessage.innerHTML = logHtml;
            errorSection.style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorSection').style.display = 'none';
        }

        function showResults(rdlContent, rptData, conversionLog = []) {
            convertedRDL = rdlContent;
            conversionStats = rptData;

            hideProgress();

            // Update summary cards
            updateSummaryCards(rptData);

            // Show RDL preview with syntax highlighting
            showRDLPreview(rdlContent);

            // Show conversion log
            showConversionLog(conversionLog);

            // Setup download button
            setupDownloadButton();

            // Show results section
            document.getElementById('resultsSection').style.display = 'block';
        }

        function showRDLPreview(rdlContent) {
            const previewElement = document.getElementById('rdlPreview');

            // Basic syntax highlighting for XML
            const highlightedXML = highlightXML(rdlContent);
            previewElement.innerHTML = highlightedXML;
        }

        function highlightXML(xml) {
            return xml
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/(&lt;\/?)([\w-]+)(.*?)(&gt;)/g,
                    '<span style="color: #0066cc;">$1</span><span style="color: #cc0066; font-weight: bold;">$2</span><span style="color: #0066cc;">$3$4</span>')
                .replace(/([\w-]+)(=)(".*?")/g,
                    '<span style="color: #cc6600;">$1</span><span style="color: #666;">$2</span><span style="color: #009900;">$3</span>');
        }

        function showConversionLog(conversionLog) {
            if (!conversionLog || conversionLog.length === 0) return;

            const logHtml = `
                <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px; color: #495057;">Conversion Log</h4>
                    <ul style="margin: 0; padding-left: 20px; font-size: 0.9em;">
                        ${conversionLog.map(entry => `<li style="margin-bottom: 3px;">${entry}</li>`).join('')}
                    </ul>
                </div>
            `;

            // Insert log before RDL preview
            const rdlPreviewContainer = document.querySelector('.rdl-preview');
            rdlPreviewContainer.insertAdjacentHTML('beforebegin', logHtml);
        }

        function updateSummaryCards(stats) {
            const summaryHtml = `
                <div class="summary-card">
                    <div class="summary-number">${stats.dataSources?.length || 0}</div>
                    <div class="summary-label">Data Sources</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">${stats.fields?.length || 0}</div>
                    <div class="summary-label">Fields Converted</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">${stats.formulas?.length || 0}</div>
                    <div class="summary-label">Formulas Converted</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">${stats.parameters?.length || 0}</div>
                    <div class="summary-label">Parameters</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">${stats.sections?.length || 0}</div>
                    <div class="summary-label">Report Sections</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">${stats.reportObjects?.length || 0}</div>
                    <div class="summary-label">Report Objects</div>
                </div>
            `;
            document.getElementById('conversionSummary').innerHTML = summaryHtml;
        }

        function setupDownloadButton() {
            const downloadBtn = document.getElementById('downloadBtn');
            downloadBtn.onclick = function() {
                try {
                    // Create formatted RDL content
                    const formattedRDL = formatXML(convertedRDL);

                    // Create blob and download
                    const blob = new Blob([formattedRDL], { type: 'application/xml' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = currentFileName.replace('.rpt', '.rdl');
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    // Show success message
                    showTemporaryMessage('RDL file downloaded successfully!', 'success');

                } catch (error) {
                    console.error('Download error:', error);
                    showTemporaryMessage('Error downloading file. Please try again.', 'error');
                }
            };
        }

        function formatXML(xml) {
            // Basic XML formatting for better readability
            let formatted = xml.replace(/></g, '>\n<');
            const lines = formatted.split('\n');
            let indent = 0;
            const indentStr = '  ';

            return lines.map(line => {
                const trimmed = line.trim();
                if (trimmed.startsWith('</')) {
                    indent--;
                }
                const result = indentStr.repeat(Math.max(0, indent)) + trimmed;
                if (trimmed.startsWith('<') && !trimmed.startsWith('</') && !trimmed.endsWith('/>')) {
                    indent++;
                }
                return result;
            }).join('\n');
        }

        function showTemporaryMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                animation: slideIn 0.3s ease-out;
                ${type === 'success' ? 'background: #28a745;' : 'background: #dc3545;'}
            `;
            messageDiv.textContent = message;

            // Add animation styles
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(messageDiv);

            // Remove after 3 seconds
            setTimeout(() => {
                messageDiv.style.animation = 'slideIn 0.3s ease-out reverse';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                    if (style.parentNode) {
                        style.parentNode.removeChild(style);
                    }
                }, 300);
            }, 3000);
        }

        function resetConverter() {
            // Reset all sections
            document.getElementById('progressSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('errorSection').style.display = 'none';

            // Reset progress bar
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = 'Initializing conversion...';

            // Reset file input
            document.getElementById('fileInput').value = '';

            // Clear any conversion logs
            const existingLogs = document.querySelectorAll('.rdl-preview').forEach(preview => {
                const prevSibling = preview.previousElementSibling;
                if (prevSibling && prevSibling.innerHTML.includes('Conversion Log')) {
                    prevSibling.remove();
                }
            });

            // Reset RDL preview
            document.getElementById('rdlPreview').textContent = '';

            // Reset summary cards
            document.getElementById('conversionSummary').innerHTML = '';

            // Reset global variables
            convertedRDL = '';
            conversionStats = {};
            currentFileName = '';

            // Show success message
            showTemporaryMessage('Converter reset successfully. Ready for new file.', 'success');
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+O or Cmd+O to open file dialog
            if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
                e.preventDefault();
                document.getElementById('fileInput').click();
            }

            // Escape to reset converter
            if (e.key === 'Escape') {
                resetConverter();
            }

            // Ctrl+S or Cmd+S to download (if RDL is available)
            if ((e.ctrlKey || e.metaKey) && e.key === 's' && convertedRDL) {
                e.preventDefault();
                document.getElementById('downloadBtn').click();
            }
        });

        // Add helpful tooltips and instructions
        function addTooltips() {
            const uploadZone = document.getElementById('uploadZone');
            uploadZone.title = 'Drag and drop your .rpt file here, or click to browse.\nKeyboard shortcut: Ctrl+O (Cmd+O on Mac)';

            // Add version info to footer
            const versionInfo = document.createElement('div');
            versionInfo.style.cssText = `
                text-align: center;
                padding: 20px;
                color: #666;
                font-size: 0.8em;
                border-top: 1px solid #eee;
                margin-top: 40px;
            `;
            versionInfo.innerHTML = `
                <p>RPT to SSRS RDL Converter v1.0 |
                Supports Crystal Reports 8.5-2020 |
                Generates SSRS 2008+ compatible RDL</p>
                <p>Keyboard shortcuts: Ctrl+O (Open), Ctrl+S (Save), Esc (Reset)</p>
            `;
            document.querySelector('.main-content').appendChild(versionInfo);
        }

        // Initialize tooltips when page loads
        document.addEventListener('DOMContentLoaded', function() {
            addTooltips();
        });

        // Demo function for testing without an actual RPT file
        async function runDemo() {
            currentFileName = 'SampleReport.rpt';
            hideError();
            showProgress();

            try {
                updateProgress(10, 'Generating sample RPT data...');
                await new Promise(resolve => setTimeout(resolve, 500));

                // Create sample RPT data
                const sampleRptData = {
                    version: '2020',
                    dataSources: [
                        {
                            id: 1,
                            name: 'NorthwindDB',
                            type: 'SQL',
                            server: 'localhost',
                            database: 'Northwind',
                            connectionString: 'Data Source=localhost;Initial Catalog=Northwind;Integrated Security=True'
                        }
                    ],
                    fields: [
                        { name: 'CustomerID', type: 'String', length: 5, tableName: 'Customers', isFormula: false },
                        { name: 'CompanyName', type: 'String', length: 40, tableName: 'Customers', isFormula: false },
                        { name: 'ContactName', type: 'String', length: 30, tableName: 'Customers', isFormula: false },
                        { name: 'OrderID', type: 'Number', length: 4, tableName: 'Orders', isFormula: false },
                        { name: 'OrderDate', type: 'Date', length: 8, tableName: 'Orders', isFormula: false },
                        { name: 'TotalAmount', type: 'Currency', length: 8, tableName: 'Orders', isFormula: false }
                    ],
                    formulas: [
                        { name: 'FullName', expression: '{Customers.ContactName} + " (" + {Customers.CompanyName} + ")"', returnType: 'String', syntax: 'Crystal' },
                        { name: 'OrderYear', expression: 'Year({Orders.OrderDate})', returnType: 'Number', syntax: 'Crystal' },
                        { name: 'FormattedAmount', expression: 'ToText({Orders.TotalAmount}, "$#,##0.00")', returnType: 'String', syntax: 'Crystal' }
                    ],
                    parameters: [
                        { name: 'StartDate', type: 'DateTime', prompt: 'Enter Start Date', defaultValue: '', allowNull: false },
                        { name: 'EndDate', type: 'DateTime', prompt: 'Enter End Date', defaultValue: '', allowNull: false },
                        { name: 'CustomerType', type: 'String', prompt: 'Select Customer Type', defaultValue: 'All', allowNull: true }
                    ],
                    sections: [
                        { name: 'ReportHeader', type: 'ReportHeader', height: 0.75, visible: true },
                        { name: 'PageHeader', type: 'PageHeader', height: 0.5, visible: true },
                        { name: 'Details', type: 'Details', height: 0.25, visible: true },
                        { name: 'PageFooter', type: 'PageFooter', height: 0.25, visible: true },
                        { name: 'ReportFooter', type: 'ReportFooter', height: 0.5, visible: true }
                    ],
                    reportObjects: [
                        { type: 'Text', name: 'Title', left: 0.5, top: 0.25, width: 4, height: 0.5, content: 'Customer Orders Report', section: 'ReportHeader' },
                        { type: 'Text', name: 'CustomerHeader', left: 0.5, top: 0.1, width: 1.5, height: 0.25, content: 'Customer', section: 'PageHeader' },
                        { type: 'Text', name: 'OrderHeader', left: 2.5, top: 0.1, width: 1, height: 0.25, content: 'Order ID', section: 'PageHeader' },
                        { type: 'Text', name: 'DateHeader', left: 4, top: 0.1, width: 1, height: 0.25, content: 'Date', section: 'PageHeader' },
                        { type: 'Text', name: 'AmountHeader', left: 5.5, top: 0.1, width: 1, height: 0.25, content: 'Amount', section: 'PageHeader' },
                        { type: 'Field', name: 'CustomerField', left: 0.5, top: 0.05, width: 1.5, height: 0.2, fieldName: 'CompanyName', section: 'Details' },
                        { type: 'Field', name: 'OrderField', left: 2.5, top: 0.05, width: 1, height: 0.2, fieldName: 'OrderID', section: 'Details' },
                        { type: 'Field', name: 'DateField', left: 4, top: 0.05, width: 1, height: 0.2, fieldName: 'OrderDate', section: 'Details' },
                        { type: 'Formula', name: 'AmountField', left: 5.5, top: 0.05, width: 1, height: 0.2, fieldName: 'FormattedAmount', section: 'Details' }
                    ],
                    grouping: [
                        { fieldName: 'CustomerID', sortOrder: 'Ascending', keepTogether: true }
                    ],
                    sorting: [
                        { fieldName: 'OrderDate', sortOrder: 'Descending' }
                    ]
                };

                updateProgress(40, 'Converting sample formulas...');
                await new Promise(resolve => setTimeout(resolve, 800));

                const sampleFormulas = await convertFormulas(sampleRptData);

                updateProgress(70, 'Generating sample RDL...');
                await new Promise(resolve => setTimeout(resolve, 1000));

                const sampleRDL = await generateRDL(sampleRptData, sampleFormulas);

                updateProgress(100, 'Demo conversion complete!');

                const demoLog = [
                    '✓ Demo mode: Using sample Crystal Reports data',
                    '✓ Successfully parsed sample report structure',
                    '✓ Converted 3 sample formulas to VB.NET expressions',
                    '✓ Generated SSRS RDL with customer orders layout',
                    '✓ Demo conversion completed successfully'
                ];

                setTimeout(() => {
                    showResults(sampleRDL, sampleRptData, demoLog);
                }, 500);

            } catch (error) {
                console.error('Demo error:', error);
                showError(`Demo failed: ${error.message}`);
                hideProgress();
            }
        }
    </script>
</body>
</html>
